apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-celery-worker
  namespace: timetable-system
  labels:
    app: analytics-celery-worker
    component: worker
spec:
  replicas: 3
  selector:
    matchLabels:
      app: analytics-celery-worker
  template:
    metadata:
      labels:
        app: analytics-celery-worker
        component: worker
    spec:
      containers:
      - name: celery-worker
        image: timetable/analytics-service:latest
        command: ["celery", "-A", "analytics_service", "worker", "--loglevel=info", "--concurrency=2", "--queues=analytics,reports,dashboard,default"]
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: ANALYTICS_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        - name: USER_SERVICE_URL
          value: "http://user-service:8002"
        - name: AUTH_SERVICE_URL
          value: "http://auth-service:8001"
        - name: PREFERENCE_SERVICE_URL
          value: "http://preference-service:8003"
        - name: TIMETABLE_SERVICE_URL
          value: "http://timetable-service:8004"
        - name: NOTIFICATION_SERVICE_URL
          value: "http://notification-service:8005"
        envFrom:
        - configMapRef:
            name: analytics-config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: media-files
          mountPath: /app/media
        - name: logs
          mountPath: /app/logs
        - name: reports
          mountPath: /app/media/reports
      volumes:
      - name: media-files
        persistentVolumeClaim:
          claimName: analytics-media-pvc
      - name: logs
        persistentVolumeClaim:
          claimName: analytics-logs-pvc
      - name: reports
        persistentVolumeClaim:
          claimName: analytics-reports-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: analytics-celery-beat
  namespace: timetable-system
  labels:
    app: analytics-celery-beat
    component: scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: analytics-celery-beat
  template:
    metadata:
      labels:
        app: analytics-celery-beat
        component: scheduler
    spec:
      containers:
      - name: celery-beat
        image: timetable/analytics-service:latest
        command: ["celery", "-A", "analytics_service", "beat", "--loglevel=info", "--scheduler=django_celery_beat.schedulers:DatabaseScheduler"]
        env:
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: MONGODB_URI
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: REDIS_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: ANALYTICS_SECRET_KEY
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: JWT_SECRET_KEY
        - name: INTERNAL_SERVICE_TOKEN
          valueFrom:
            secretKeyRef:
              name: timetable-secrets
              key: INTERNAL_SERVICE_TOKEN
        envFrom:
        - configMapRef:
            name: analytics-config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: analytics-logs-pvc
